
import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { 
  Home, 
  Users, 
  BookOpen, 
  Calendar, 
  DollarSign, 
  Settings, 
  LogOut, 
  ClipboardList,
  MessagesSquare,
  BarChart3,
  DoorOpen,
  FolderCheck,
  ChevronLeft,
  ChevronRight,
  X
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import ThemeToggle from "@/components/common/ThemeToggle";
import { getCurrentUser, logoutUser, hasPermission } from "@/lib/auth";
import { UserRole } from "@/types";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

interface SidebarItem {
  name: string;
  icon: React.ElementType;
  href: string;
  requiredPermission?: UserRole[];
}

interface SidebarProps {
  isCollapsed: boolean;
  setIsCollapsed: () => void;
  isMobile: boolean;
}

const Sidebar = ({ isCollapsed, setIsCollapsed, isMobile }: SidebarProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [userData, setUserData] = useState<{name: string, role: UserRole} | null>(null);

  // Load user data
  useEffect(() => {
    const fetchUser = async () => {
      const user = await getCurrentUser();
      if (user) {
        setUserData({
          name: user.name || 'User',
          role: user.role || 'Guest'
        });
      }
    };
    
    fetchUser();
  }, []);

  const handleLogout = () => {
    logoutUser();
    toast.success("You have been logged out.");
    navigate("/");
  };

  const sidebarItems: SidebarItem[] = [
    { name: "Dashboard", icon: Home, href: "/dashboard" },
    { name: "Students", icon: Users, href: "/students" },
    { name: "Classes", icon: BookOpen, href: "/classes" },
    { name: "Attendance", icon: ClipboardList, href: "/attendance" },
    { name: "Schedule", icon: Calendar, href: "/schedule" },
    { name: "Payments", icon: DollarSign, href: "/payments" },
    { name: "Rooms", icon: DoorOpen, href: "/rooms" },
    { name: "Notes", icon: MessagesSquare, href: "/notes" },
    { name: "Reports", icon: BarChart3, href: "/reports" },
    { name: "Activity", icon: Calendar, href: "/activity-log", requiredPermission: ["superAdmin", "manager"] },
    { name: "Users", icon: Users, href: "/users", requiredPermission: ["superAdmin", "manager"] },
    { name: "Settings", icon: Settings, href: "/settings", requiredPermission: ["superAdmin", "manager"] }
  ];

  const filteredItems = sidebarItems.filter(item => {
    if (!item.requiredPermission) return true;
    return hasPermission(item.requiredPermission);
  });

  if (isCollapsed && isMobile) {
    return null; // Don't render sidebar at all when collapsed on mobile
  }

  return (
    <div 
      className={cn(
        "h-full flex flex-col border-r transition-all duration-300 ease-in-out bg-white dark:bg-gray-900",
        isCollapsed ? "w-16" : "w-64",
        isMobile ? "fixed inset-y-0 left-0 z-50 shadow-lg" : "relative"
      )}
    >
      <div className="h-16 px-3 border-b flex items-center justify-between">
        {!isCollapsed && (
          <h1 className="text-2xl font-bold text-primary flex items-center">
            <FolderCheck className="mr-2 h-6 w-6" />
            Vertex
          </h1>
        )}
        {isCollapsed && (
          <div className="mx-auto flex items-center justify-center">
            <FolderCheck className="h-7 w-7 text-primary" />
          </div>
        )}
        {!isMobile && (
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={setIsCollapsed}
            className="text-muted-foreground hover:text-foreground"
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
          </Button>
        )}
        {isMobile && !isCollapsed && (
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={setIsCollapsed}
            className="text-muted-foreground hover:text-foreground"
          >
            <X size={18} />
          </Button>
        )}
      </div>
      <ScrollArea className="flex-1">
        <div className="space-y-1 py-4 px-2">
          {filteredItems.map((item) => (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                location.pathname === item.href || 
                (item.href !== "/dashboard" && location.pathname.startsWith(item.href))
                  ? "bg-accent text-accent-foreground"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                isCollapsed && "justify-center"
              )}
            >
              <item.icon className="h-5 w-5" />
              {!isCollapsed && <span>{item.name}</span>}
            </Link>
          ))}
        </div>
      </ScrollArea>
      <div className="border-t p-3 space-y-2">
        {!isCollapsed ? (
          <>
            <div className="flex items-center justify-between p-2">
              <div className="text-sm">
                <div className="font-medium">{userData?.name || 'Guest'}</div>
                <div className="text-xs text-muted-foreground">{userData?.role || 'Guest'}</div>
              </div>
              <ThemeToggle />
            </div>
            <Button
              variant="outline"
              className="w-full flex items-center justify-center gap-3"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
          </>
        ) : (
          <div className="flex flex-col items-center space-y-3">
            <ThemeToggle />
            <Button
              variant="outline"
              size="icon"
              onClick={handleLogout}
              className="w-10 h-10"
              aria-label="Logout"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
