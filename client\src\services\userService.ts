// client/src/services/userService.ts
import { usersApi } from '@/lib/api';

export interface User {
  _id: string;
  username: string;
  role: 'superAdmin' | 'manager' | 'secretary' | 'teacher';
  status: 'active' | 'inactive';
  lastLogin?: string;
  createdAt: string;
  modifiedAt: string;
}

export interface UserFilter {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  status?: 'active' | 'inactive';
  role?: string;
  search?: string;
  fromDate?: string;
  toDate?: string;
  activityStatus?: 'online' | 'offline' | 'idle';
}

export interface UserResponse {
  success: boolean;
  data: User[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

/**
 * Fetch users from the backend API
 */
export const fetchUsers = async (filters: UserFilter = {}): Promise<UserResponse> => {
  console.log('fetchUsers called with filters:', filters);
  try {
    const params: Record<string, any> = {};
    
    // Add pagination
    if (filters.page) params.page = filters.page;
    if (filters.limit) params.limit = filters.limit;
    
    // Add sorting
    if (filters.sortBy) params.sortBy = filters.sortBy;
    if (filters.sortOrder) params.sortOrder = filters.sortOrder;
    
    // Add filters
    if (filters.status) params.status = filters.status;
    if (filters.role) params.role = filters.role;
    if (filters.search) params.search = filters.search;
    if (filters.fromDate) params.fromDate = filters.fromDate;
    if (filters.toDate) params.toDate = filters.toDate;
    if (filters.activityStatus) params.activityStatus = filters.activityStatus;

    console.log('Making API call to usersApi.getUsers with params:', params);
    const response = await usersApi.getUsers(params);
    console.log('API response:', response);

    return {
      success: true,
      data: response.data || [],
      pagination: response.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0
      }
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    console.error('Error details:', error instanceof Error ? error.message : error);
    return {
      success: false,
      data: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0
      }
    };
  }
};

/**
 * Fetch a single user by ID
 */
export const fetchUserById = async (id: string): Promise<{ success: boolean; data?: User; error?: string }> => {
  try {
    const response = await usersApi.getUser(id);
    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('Error fetching user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch user'
    };
  }
};

/**
 * Update user status
 */
export const updateUserStatus = async (
  userId: string, 
  status: 'active' | 'inactive', 
  reason: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    await usersApi.updateUserStatus(userId, { status, reason });
    return { success: true };
  } catch (error) {
    console.error('Error updating user status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update user status'
    };
  }
};

/**
 * Update user role
 */
export const updateUserRole = async (
  userId: string,
  role: string,
  reason: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    await usersApi.updateUserRole(userId, { role, reason });
    return { success: true };
  } catch (error) {
    console.error('Error updating user role:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update user role'
    };
  }
};

/**
 * Bulk update user status
 */
export const bulkUpdateUserStatus = async (
  userIds: string[],
  status: 'active' | 'inactive',
  reason: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    await usersApi.bulkUpdateStatus({ userIds, status, reason });
    return { success: true };
  } catch (error) {
    console.error('Error bulk updating user status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to bulk update user status'
    };
  }
};

/**
 * Bulk update user role
 */
export const bulkUpdateUserRole = async (
  userIds: string[],
  role: string,
  reason: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    await usersApi.bulkUpdateRole({ userIds, role, reason });
    return { success: true };
  } catch (error) {
    console.error('Error bulk updating user role:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to bulk update user role'
    };
  }
};
