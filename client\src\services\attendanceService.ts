
import {
  AttendanceRecord,
  AttendanceFilter,
  AttendanceResponse,
  StudentAttendanceStats,
  ClassAttendanceStats,
  AttendanceStatus,
  ExcuseStatus,
  StudentAttendance
} from "@/types/attendance";

const API_BASE_URL = 'http://localhost:3000/api';

// Helper function to get auth headers
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
  'Content-Type': 'application/json',
});

// Fetch attendance records with filtering

export const fetchAttendanceRecords = async (filter: AttendanceFilter): Promise<AttendanceResponse> => {
  try {
    const queryParams = new URLSearchParams();

    // Add filter parameters
    if (filter.page) queryParams.append('page', filter.page.toString());
    if (filter.limit) queryParams.append('limit', filter.limit.toString());
    if (filter.sortBy) queryParams.append('sortBy', filter.sortBy);
    if (filter.sortOrder) queryParams.append('sortOrder', filter.sortOrder);
    if (filter.classId) queryParams.append('classId', filter.classId);
    if (filter.teacherId) queryParams.append('teacherId', filter.teacherId);
    if (filter.studentId) queryParams.append('studentId', filter.studentId);
    if (filter.status) queryParams.append('status', filter.status);
    if (filter.startDate) queryParams.append('startDate', filter.startDate);
    if (filter.endDate) queryParams.append('endDate', filter.endDate);
    if (filter.isMakeupClass !== undefined) queryParams.append('isMakeupClass', filter.isMakeupClass.toString());

    const response = await fetch(`${API_BASE_URL}/attendance?${queryParams}`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();

    // Backend returns: { success: true, data: [records], pagination: {...} }
    // We need to construct: { records: [...], pagination: {...} }
    if (responseData.success && responseData.data && responseData.pagination) {
      return {
        records: Array.isArray(responseData.data) ? responseData.data : [],
        pagination: {
          total: responseData.pagination.total || 0,
          page: responseData.pagination.page || 1,
          limit: responseData.pagination.limit || 10,
          totalPages: responseData.pagination.pages || 0
        }
      };
    }

    // Fallback for unexpected response format
    return { records: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } };
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    // Return empty data instead of throwing to prevent UI crashes
    return { records: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } };
  }
};

// Get student attendance statistics
export const fetchStudentAttendanceStats = async (studentId: string): Promise<StudentAttendanceStats> => {
  try {
    const response = await fetch(`${API_BASE_URL}/attendance/students/${studentId}/stats`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching student attendance stats:', error);
    // Return empty stats instead of throwing
    return {
      studentId,
      studentName: 'Unknown Student',
      overall: { total: 0, present: 0, absent: 0, late: 0, excused: 0, rate: 0 },
      byClass: [],
      byWeekday: [],
      trends: []
    };
  }
};
  
// Get class attendance statistics
export const fetchClassAttendanceStats = async (classId: string): Promise<ClassAttendanceStats> => {
  try {
    const response = await fetch(`${API_BASE_URL}/attendance/classes/${classId}/stats`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching class attendance stats:', error);
    // Return empty stats instead of throwing
    return {
      classId,
      className: 'Unknown Class',
      overall: { total: 0, present: 0, absent: 0, late: 0, excused: 0, rate: 0 },
      byStudent: [],
      byDate: [],
      byWeekday: []
    };
  }
};

// Mark attendance for a single student
export const markAttendance = async (
  classId: string,
  date: string,
  studentId: string,
  status: AttendanceStatus,
  arrivalTime?: string,
  excuse?: { reason: string, documentUrl?: string },
  notes?: string
): Promise<{ success: boolean }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/attendance/classes/${classId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        date,
        students: [{
          studentId,
          status,
          arrivalTime,
          excuse,
          notes
        }]
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error marking attendance:', error);
    return { success: false };
  }
};
// Bulk mark attendance
export const bulkMarkAttendance = async (
  classId: string,
  date: string,
  records: { studentId: string, status: AttendanceStatus }[]
): Promise<{ success: boolean }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/attendance/classes/${classId}/bulk`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        date,
        students: records
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error bulk marking attendance:', error);
    return { success: false };
  }
};

// Add an excuse
export const addExcuse = async (
  classId: string,
  date: string,
  studentId: string,
  reason: string,
  documentUrl?: string,
  notes?: string
): Promise<{ success: boolean }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/attendance/classes/${classId}/excuse`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        date,
        studentId,
        reason,
        documentUrl,
        notes
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error adding excuse:', error);
    return { success: false };
  }
};

// Verify excuse
export const verifyExcuse = async (
  classId: string,
  date: string,
  studentId: string,
  status: 'approved' | 'rejected'
): Promise<{ success: boolean }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/attendance/classes/${classId}/students/${studentId}/verify-excuse`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        date,
        status
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error verifying excuse:', error);
    return { success: false };
  }
};

// Get available classes for attendance
export const fetchClassesForAttendance = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/classes`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching classes:', error);
    return [];
  }
};

// Get available students
export const fetchStudents = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/students`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching students:', error);
    return [];
  }
};

// Export attendance data
export const exportAttendance = async (
  format: 'csv' | 'json',
  filter: AttendanceFilter
): Promise<Blob> => {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append('format', format);

    // Add filter parameters
    if (filter.classId) queryParams.append('classId', filter.classId);
    if (filter.teacherId) queryParams.append('teacherId', filter.teacherId);
    if (filter.studentId) queryParams.append('studentId', filter.studentId);
    if (filter.status) queryParams.append('status', filter.status);
    if (filter.startDate) queryParams.append('startDate', filter.startDate);
    if (filter.endDate) queryParams.append('endDate', filter.endDate);
    if (filter.isMakeupClass !== undefined) queryParams.append('isMakeupClass', filter.isMakeupClass.toString());

    const response = await fetch(`${API_BASE_URL}/attendance/export?${queryParams}`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.blob();
  } catch (error) {
    console.error('Error exporting attendance:', error);
    // Return empty blob on error
    return new Blob([''], { type: format === 'json' ? 'application/json' : 'text/csv' });
  }
};
