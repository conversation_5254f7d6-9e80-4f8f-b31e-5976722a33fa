
import { useState } from "react";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ListChecks, UserPlus, UserX } from "lucide-react";

interface StudentBulkActionsProps {
  selectedCount: number;
  selectedIds: string[];
  onCancel: () => void;
  onComplete: () => void;
}

type BulkOperation = "activate" | "deactivate" | "changeLevel" | "assignClass";

const StudentBulkActions = ({
  selectedCount,
  selectedIds,
  onCancel,
  onComplete,
}: StudentBulkActionsProps) => {
  const [operation, setOperation] = useState<BulkOperation | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [level, setLevel] = useState("");
  const [classId, setClassId] = useState("");

  const handleOperationChange = (value: string) => {
    setOperation(value as BulkOperation);
    setIsDialogOpen(true);
  };

  const handleConfirm = () => {
    if (operation) {
      // Perform bulk action based on selected operation
      switch (operation) {
        case "activate":
          toast.success(`Activated ${selectedCount} students`);
          break;
        case "deactivate":
          toast.success(`Deactivated ${selectedCount} students`);
          break;
        case "changeLevel":
          toast.success(
            `Changed level for ${selectedCount} students to ${level}`
          );
          break;
        case "assignClass":
          toast.success(
            `Assigned ${selectedCount} students to class ${classId}`
          );
          break;
        default:
          toast.error("Invalid operation");
          break;
      }

      setIsDialogOpen(false);
      onComplete();
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full text-sm font-medium">
          {selectedCount}
        </div>
        <div>
          <p className="text-sm font-medium">
            {selectedCount} student{selectedCount === 1 ? "" : "s"} selected
          </p>
          <p className="text-xs text-muted-foreground">
            Choose an action to apply to selected students
          </p>
        </div>
      </div>

      <div className="flex items-center gap-3">
        <Select onValueChange={handleOperationChange}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Choose bulk action" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="activate">
              <div className="flex items-center">
                <UserPlus className="mr-2 h-4 w-4" />
                <span>Activate Students</span>
              </div>
            </SelectItem>
            <SelectItem value="deactivate">
              <div className="flex items-center">
                <UserX className="mr-2 h-4 w-4" />
                <span>Deactivate Students</span>
              </div>
            </SelectItem>
            <SelectItem value="changeLevel">
              <div className="flex items-center">
                <ListChecks className="mr-2 h-4 w-4" />
                <span>Change Level</span>
              </div>
            </SelectItem>
            <SelectItem value="assignClass">
              <div className="flex items-center">
                <ListChecks className="mr-2 h-4 w-4" />
                <span>Assign to Class</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" onClick={onCancel}>
          Cancel Selection
        </Button>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Bulk Action</DialogTitle>
            <DialogDescription>
              Are you sure you want to perform this action on{" "}
              {selectedCount} students?
            </DialogDescription>
          </DialogHeader>
          {operation === "changeLevel" && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="level" className="text-right">
                  Level
                </Label>
                <Input
                  type="text"
                  id="level"
                  value={level}
                  onChange={(e) => setLevel(e.target.value)}
                  className="col-span-3"
                />
              </div>
            </div>
          )}
          {operation === "assignClass" && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="classId" className="text-right">
                  Class ID
                </Label>
                <Input
                  type="text"
                  id="classId"
                  value={classId}
                  onChange={(e) => setClassId(e.target.value)}
                  className="col-span-3"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="secondary" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirm}>Confirm</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StudentBulkActions;
