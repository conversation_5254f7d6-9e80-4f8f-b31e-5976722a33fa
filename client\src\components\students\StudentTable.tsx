
import { useState } from "react";
import { Student } from "@/types/student";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  ChevronDown, 
  Edit, 
  Eye, 
  MoreHorizontal, 
  Plus,
  Search, 
  Trash2,
  Archive,
  CreditCard,
  Repeat
} from "lucide-react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface StudentTableProps {
  students: Student[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
  isLoading: boolean;
  isError: boolean;
  selectedIds: string[];
  onSelectedIdsChange: (ids: string[]) => void;
  onPageChange: (page: number) => void;
  onRefresh: () => void;
}

const StudentTable = ({ 
  students = [], // Set default empty array to avoid undefined errors
  pagination, 
  isLoading, 
  isError, 
  selectedIds = [], // Set default empty array to avoid undefined errors
  onSelectedIdsChange = () => {}, // Default no-op function
  onPageChange = () => {}, // Default no-op function
  onRefresh = () => {} // Default no-op function
}: StudentTableProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  // Filter students based on search term
  const filteredStudents = students ? students.filter((student) =>
    `${student.firstName} ${student.lastName}`
      .toLowerCase()
      .includes(searchTerm.toLowerCase()) || 
    student.contactInfo.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.contactInfo.phone?.includes(searchTerm)
  ) : [];

  // Handle checkbox selection
  const handleSelectAll = () => {
    if (selectedIds.length === filteredStudents.length) {
      onSelectedIdsChange([]);
    } else {
      onSelectedIdsChange(filteredStudents.map(student => student.id));
    }
  };

  const handleSelectOne = (studentId: string) => {
    if (selectedIds.includes(studentId)) {
      onSelectedIdsChange(selectedIds.filter(id => id !== studentId));
    } else {
      onSelectedIdsChange([...selectedIds, studentId]);
    }
  };

  // Generate pagination items
  const generatePaginationItems = () => {
    if (!pagination) return null;
    
    const { page, pages } = pagination;
    const items = [];
    
    // Previous button
    items.push(
      <PaginationItem key="prev">
        <PaginationPrevious 
          onClick={() => page > 1 && onPageChange(page - 1)}
          className={page <= 1 ? "pointer-events-none opacity-50" : ""}
        />
      </PaginationItem>
    );
    
    // First page
    items.push(
      <PaginationItem key="page-1">
        <PaginationLink 
          isActive={page === 1}
          onClick={() => onPageChange(1)}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );
    
    // Ellipsis if needed
    if (page > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    // Pages around current
    for (let i = Math.max(2, page - 1); i <= Math.min(pages - 1, page + 1); i++) {
      items.push(
        <PaginationItem key={`page-${i}`}>
          <PaginationLink 
            isActive={page === i}
            onClick={() => onPageChange(i)}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    // Ellipsis if needed
    if (page < pages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    // Last page if more than 1 page
    if (pages > 1) {
      items.push(
        <PaginationItem key={`page-${pages}`}>
          <PaginationLink 
            isActive={page === pages}
            onClick={() => onPageChange(pages)}
          >
            {pages}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    // Next button
    items.push(
      <PaginationItem key="next">
        <PaginationNext 
          onClick={() => page < pages && onPageChange(page + 1)}
          className={page >= pages ? "pointer-events-none opacity-50" : ""}
        />
      </PaginationItem>
    );
    
    return items;
  };

  if (isLoading) {
    return (
      <div className="border rounded-lg p-8 flex flex-col items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mb-4"></div>
        <p className="text-muted-foreground">Loading students...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="border rounded-lg p-8 flex flex-col items-center justify-center">
        <p className="text-red-500 mb-4">Failed to load students</p>
        <Button onClick={onRefresh} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Table Container */}
      <div className="rounded-lg overflow-hidden border bg-card">
        <Table>
          <TableHeader className="bg-muted/30">
            <TableRow className="border-b">
              <TableHead className="w-[50px] pl-6">
                <Checkbox
                  checked={selectedIds && filteredStudents && selectedIds.length > 0 && selectedIds.length === filteredStudents.length}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead className="font-semibold">Name</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold">Level</TableHead>
              <TableHead className="hidden md:table-cell font-semibold">Class</TableHead>
              <TableHead className="hidden md:table-cell font-semibold">Email</TableHead>
              <TableHead className="hidden lg:table-cell font-semibold">Phone</TableHead>
              <TableHead className="hidden lg:table-cell font-semibold">Payment</TableHead>
              <TableHead className="text-right font-semibold pr-6">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredStudents && filteredStudents.length > 0 ? (
              filteredStudents.map((student) => (
                <TableRow key={student.id} className="group hover:bg-muted/50 transition-colors">
                  <TableCell className="pl-6">
                    <Checkbox
                      checked={selectedIds && selectedIds.includes(student.id)}
                      onCheckedChange={() => handleSelectOne(student.id)}
                    />
                  </TableCell>
                  <TableCell className="font-medium py-4">
                    <div className="flex flex-col">
                      <span>{student.firstName} {student.lastName}</span>
                      <span className="text-sm text-muted-foreground md:hidden">
                        {student.contactInfo.email}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={cn(
                        "capitalize",
                        student.status === "active" && "border-green-200 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 dark:border-green-900",
                        student.status === "inactive" && "border-red-200 bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 dark:border-red-900",
                        student.status === "pending" && "border-yellow-200 bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-900",
                        student.status === "archived" && "border-gray-200 bg-gray-50 text-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700"
                      )}
                    >
                      {student.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {student.currentLevel}
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    {student.currentClass?.name || "—"}
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    {student.contactInfo.email || "—"}
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    {student.contactInfo.phone || "—"}
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <Badge
                      variant="outline"
                      className={cn(
                        "capitalize",
                        student.paymentStatus === "paid" && "border-green-200 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 dark:border-green-900",
                        student.paymentStatus === "pending" && "border-yellow-200 bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-900",
                        student.paymentStatus === "overdue" && "border-red-200 bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 dark:border-red-900"
                      )}
                    >
                      {student.paymentStatus || "—"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right pr-6">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="opacity-70 group-hover:opacity-100 h-8 w-8"
                        >
                          <MoreHorizontal size={16} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Student Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild className="cursor-pointer">
                          <Link to={`/students/${student.id}`}>
                            <Eye size={16} className="mr-2" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild className="cursor-pointer">
                          <Link to={`/students/${student.id}/edit`}>
                            <Edit size={16} className="mr-2" />
                            Edit Student
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild className="cursor-pointer">
                          <Link to={`/students/${student.id}/payment`}>
                            <CreditCard size={16} className="mr-2" />
                            Record Payment
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild className="cursor-pointer">
                          <Link to={`/students/${student.id}/transfer`}>
                            <Repeat size={16} className="mr-2" />
                            Transfer Class
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          className="text-amber-600 cursor-pointer"
                          onClick={() => {
                            // Handle archive
                            console.log("Archive student", student.id);
                          }}
                        >
                          <Archive size={16} className="mr-2" />
                          Archive Student
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-red-600 cursor-pointer"
                          onClick={() => {
                            // Handle delete
                            console.log("Delete student", student.id);
                          }}
                        >
                          <Trash2 size={16} className="mr-2" />
                          Delete Student
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} className="h-32 text-center">
                  <div className="flex flex-col items-center justify-center space-y-3">
                    <div className="text-muted-foreground">
                      <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No students found</p>
                      <p className="text-xs text-muted-foreground/70">
                        Try adjusting your search or filters
                      </p>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && pagination.pages > 1 && (
        <div className="flex items-center justify-between px-2">
          <div className="text-sm text-muted-foreground">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} students
          </div>
          <Pagination>
            <PaginationContent>
              {generatePaginationItems()}
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};

export default StudentTable;
